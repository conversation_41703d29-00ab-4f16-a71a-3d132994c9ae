#sidebar {
    background-color: @sidebar;
    position: fixed;
    left: 0;
    top: @header-height;
    z-index: 9;
    height: ~"calc(100% - 62px)";
    
    @media (min-width: (@screen-lg-min + 80px)), (max-width: (@screen-sm-min)) { 
        width: @sidebar-left-width;
        overflow: auto;
    }
    
    @media (min-width: @screen-sm-min) and (max-width: (@screen-md-max + 80px)) {
        &:not(.toggled) {
            width: @sidebar-left-mid-width;
            overflow: visible;
        }
        
        &.toggled {
            width: @sidebar-left-width;
            overflow: auto;
        }
    }
    
    @media (max-width: @screen-sm-min)  {
        display: none;
        
        &.toggled {
            display: block;
            z-index: 12;
        }
    }
}


/* --------------------------------------------------------
    Profile Menu
-----------------------------------------------------------*/
.sms-profile {
    margin: 12px 0 10px;
    
    & > a {
        padding: 15px;
        display: block;
        color: @color-dark;
        
        & > img {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            float: left;
            margin-right: 10px;
            margin-top: 3px;
        }
    }
}


/* --------------------------------------------------------
    Sidebar Menu
-----------------------------------------------------------*/
.side-menu {
    list-style: none;
    padding: 0;
    
    a {
        color: @color-dark;
    }
    
    & > li {
        width: 100%;
        display: block;
    
        & > a {
            display: block;
            padding: 9px 10px 9px 16px;
            position: relative;
            white-space: nowrap;
            .transition(color);            

            & > .zmdi {
                font-size: 13px;
                width: 28px;
                height: 28px;
                border-radius: 50%;
                background-color: #000;
                line-height: 29px;
                margin-right: 7px;
                text-align: center;
            }
            
            .label {
                position: absolute;
                top: 15px;
                right: 12px;
            }
        }
        
        &.active > a,
        &:hover > a  {
            color: #fff;
        }
        
        &.active > a {
            background: @sidebar-active-bg;
            
            .zmdi {
                background: #2C313A;
                color: #fff;
            }
        }
    }
}

.sm-sub {
    position: relative;
    
    &:not(.active) {
        & > ul {
            display: none;
        }
    }
    
    & > ul {
        position: relative;
        width: 100%;
        padding: 0 0 0 27px;
        background: darken(@sidebar, 1.5%);
        margin-bottom: 0;
        border: 0;
        list-style: none;
        
        &:before {
            content: ""; 
            height: 100%;
            width: 1px;
            position: absolute;
            background: #1f2229;
            left: 30px;
            top: 0;
        }
        
        & > li {
            & > a {
                padding: 7px 18px 7px 28px;
                font-size: 12px;
                display: block;
                position: relative;
                white-space: nowrap;
                .transition(color);
                
                &:hover {
                    color: #fff; 
                }
                
                &:before {
                    content: "";
                    width: 8px;
                    height: 1px;
                    background: #22252d;
                    position: absolute;
                    left: 4px; 
                    top: 14px;
                }
            }
            
            &.active > a {
                color: #fff;
            }
            
            &:first-child > a {
                &:before {
                    top: 20px;
                }
                
                padding-top: 13px;
            }
            
            &:last-child > a {
                padding-bottom: 13px;
            }
        }
    }
}


/* --------------------------------------------------------
    Sidebar for mid size screens
-----------------------------------------------------------*/
@media (min-width: @screen-sm-min) and (max-width: (@screen-md-max + 80px)) {
    #sidebar:not(.toggled) {
        .side-menu > li {
            & > a {
                & > span {
                    position: absolute;
                    left: @sidebar-left-mid-width;
                    background-color: @sidebar-active-bg;
                    width: 180px;
                    padding: 14px 18px;
                    display: none;
                    text-transform: uppercase;
                    .animated(fadeIn, 300ms);
                }
                
                .label {
                    display: none;
                }
            }
            
            &.sms-bottom > a > span {
                bottom: 0;
            }
            
            &:not(.sms-bottom) > a > span {
                top: 0;
            }
            
            &:hover {
                & a > span {
                    display: block;
                }
            }
        }
        
        .sm-sub {
            & > ul {
                display: none !important;
                position: absolute;
                left: @sidebar-left-mid-width;
                width: 180px;
                padding-left: 0;
                .animated(fadeIn, 300ms);
                
                &:before {
                    display: none;
                }
                
                & > li > a {
                    padding-left: 18px;
                    
                    &:before {
                        display: none;
                    }
                }
            }
            
            &:not(.sms-bottom) > ul {
                top: 46px;
                border-top: 1px solid lighten(@sidebar, 5%);
            }
            
            &.sms-bottom > ul {
                bottom: 46px;
                border-bottom: 1px solid lighten(@sidebar, 5%);
            }
            
            &:hover {
                & > ul {
                    display: block !important;
                }
            }
        }
    }
}