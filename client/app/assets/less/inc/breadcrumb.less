.breadcrumb {
    border-bottom: 1px solid #E5E5E5;
    border-radius: 0;
    padding-top: 10px;
    padding-right: 33px;
    padding-bottom: 11px;
    
    @media (min-width: (@screen-lg-min + 80px)) { 
        padding-left: (@sidebar-left-width + @grid-gutter-width);
    }
    
    @media (min-width: @screen-sm-min) and (max-width: (@screen-md-max + 80px)) {
        padding-left: (@sidebar-left-mid-width + @grid-gutter-width);
    }
       
    @media (max-width: (@screen-sm-min)) {
        padding-left: @grid-gutter-width/2;
    }
    
    & > li {
        & > a {
            color: #A9A9A9;
            
            &:hover {
                color: @breadcrumb-active-color;
            }
        }
    }
}
