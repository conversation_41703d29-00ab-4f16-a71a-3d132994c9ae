// based on https://github.com/outbrain/tech-companies-logos-in-css/pull/28

@primary: #ff7964;
@shadow: #ef6c58;
@bar: white;

#css-logo {
  width: 100px;
  height: 100px;
  position: relative;
  
  #circle {
    width: 79px;
    height: 79px;
    background-color: @shadow;
    border-radius: 50%;
    margin: auto;
    overflow: hidden;
    position: relative;

    & > div {
      width: 79px;
      height: 73px;
      background-color: @primary;
      border-radius: 50%;
      position: absolute;
      top: 0;
    }
  }
  
  #bars {
    position: absolute;
    left: 0;
    top: 24px;
    right: 0;
    height: 33px;
    display: flex;
    padding: 0 22px 0;

    .bar {
      background: @bar;
      box-shadow: 0px 2px 0 0 @shadow;
      display: inline-block;
      border-radius: 1px;
      align-self: flex-end;
      flex: 1;
      margin: 0 2px;
      border-radius: 3px;

      &:nth-child(1) {
        height: 32%;
      }
      
      &:nth-child(2) {
        height: 71%;
      }
      
      &:nth-child(3) {
        height: 50%;
      }
      
      &:nth-child(4) {
        height: 100%;
      }
    }
  }
  
  #point,
  #point > div {
    position: absolute;
    width: 0;
    height: 0;
    border: 17px solid @shadow;
    border-right-color: transparent !important;
    border-bottom-color: transparent !important;
    bottom: 0;
    left: 48px;
    transform: scaleX(0.87);
    transform-origin: left;
  }
  
  #point > div {
    bottom: -12px;
    border-color: @primary;
    transform: scaleX(1.04);
    left: -17px;
  }
}
