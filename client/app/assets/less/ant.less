@import "~antd/lib/style/core/iconfont";
@import "~antd/lib/style/core/motion";
@import "~antd/lib/alert/style/index";
@import "~antd/lib/input/style/index";
@import "~antd/lib/input-number/style/index";
@import "~antd/lib/date-picker/style/index";
@import "~antd/lib/modal/style/index";
@import "~antd/lib/tooltip/style/index";
@import "~antd/lib/select/style/index";
@import "~antd/lib/checkbox/style/index";
@import "~antd/lib/upload/style/index";
@import "~antd/lib/form/style/index";
@import "~antd/lib/button/style/index";
@import "~antd/lib/radio/style/index";
@import "~antd/lib/time-picker/style/index";
@import "~antd/lib/pagination/style/index";
@import "~antd/lib/table/style/index";
@import "~antd/lib/popover/style/index";
@import "~antd/lib/tag/style/index";
@import "~antd/lib/grid/style/index";
@import "~antd/lib/switch/style/index";
@import "~antd/lib/empty/style/index";
@import "~antd/lib/drawer/style/index";
@import "~antd/lib/card/style/index";
@import "~antd/lib/steps/style/index";
@import "~antd/lib/divider/style/index";
@import "~antd/lib/dropdown/style/index";
@import "~antd/lib/menu/style/index";
@import "~antd/lib/list/style/index";
@import "~antd/lib/badge/style/index";
@import "~antd/lib/card/style/index";
@import "~antd/lib/spin/style/index";
@import "~antd/lib/skeleton/style/index";
@import "~antd/lib/tabs/style/index";
@import "~antd/lib/notification/style/index";
@import "~antd/lib/collapse/style/index";
@import "~antd/lib/progress/style/index";
@import "~antd/lib/typography/style/index";
@import "~antd/lib/descriptions/style/index";
@import "inc/ant-variables";

// Increase z-indexes to avoid conflicts with some other libraries (e.g. Plotly)
@zindex-modal: 2000;
@zindex-modal-mask: 2000;
@zindex-message: 2010;
@zindex-notification: 2010;
@zindex-popover: 2030;
@zindex-dropdown: 2050;
@zindex-picker: 2050;
@zindex-tooltip: 2060;
@item-hover-bg: #e5f8ff;

.@{drawer-prefix-cls} {
  &.help-drawer {
    z-index: @zindex-tooltip; // help drawer should be topmost
  }
}

// Remove bold in labels for Ant checkboxes and radio buttons
.ant-checkbox-wrapper,
.ant-radio-wrapper {
  font-weight: normal;
}

.ant-select-dropdown-menu-item em {
  color: @input-color-placeholder;
  font-size: 11px;
}

// Fix for disabled button styles inside Tooltip component.
// Tooltip wraps disabled buttons with `<span>` and moves all styles
// and classes to that `<span>`. This resets all button styles and
// turns it into simple inline element (because now it's wrapper is a button)
.btn {
  button[disabled] {
    -moz-appearance: none !important;
    -webkit-appearance: none !important;
    appearance: none !important;
    border: 0 !important;
    outline: none !important;
    background: transparent !important;
    margin: 0 !important;
    padding: 0 !important;
  }
}

// Button overrides
.@{btn-prefix-cls} {
  transition-duration: 150ms;

  &.icon-button {
    width: 32px;
    padding: 0 10px;
  }
}

// Fix ant input number showing duplicate arrows
.ant-input-number-input::-webkit-outer-spin-button,
.ant-input-number-input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

// Pagination overrides (based on existing Bootstrap overrides)
.@{pagination-prefix-cls} {
  display: inline-block;
  margin-top: 18px;
  margin-bottom: 18px;
  vertical-align: top;

  &-item {
    background-color: @pagination-bg;
    border-color: transparent;
    color: @pagination-color;
    font-size: 14px;
    margin-right: 5px;

    a {
      color: inherit;
    }

    &:focus,
    &:hover {
      background-color: @pagination-hover-bg;
      border-color: transparent;
      color: @pagination-hover-color;
      a {
        color: inherit;
      }
    }

    &-active {
      &,
      &:hover,
      &:focus {
        background-color: @pagination-active-bg;
        color: @pagination-active-color;
        border-color: transparent;
        pointer-events: none;
        cursor: default;

        a {
          color: inherit;
        }
      }
    }
  }

  &-disabled {
    &,
    &:hover,
    &:focus {
      opacity: 0.5;
      pointer-events: none;
    }
  }

  &-prev,
  &-next {
    .@{pagination-prefix-cls}-item-link {
      background-color: @pagination-bg;
      border-color: transparent;
      color: @pagination-color;
      line-height: @pagination-item-size - 2px;

      .@{pagination-prefix-cls}.mini & {
        line-height: @pagination-item-size-sm - 2px;
      }
    }

    &:focus .@{pagination-prefix-cls}-item-link,
    &:hover .@{pagination-prefix-cls}-item-link {
      background-color: @pagination-hover-bg;
      border-color: transparent;
      color: @pagination-hover-color;
    }
  }

  &-prev,
  &-jump-prev,
  &-jump-next {
    margin-right: 5px;
  }

  &-jump-prev,
  &-jump-next {
    .@{pagination-prefix-cls}-item-container {
      .@{pagination-prefix-cls}-item-link-icon {
        color: @pagination-color;
      }
    }
  }
}

// Table

.@{table-prefix-cls} {
  color: inherit;

  tr,
  th,
  td {
    transition: none !important;
  }

  &-thead > tr > th {
    padding: @table-padding-vertical * 2 @table-padding-horizontal;
  }

  .@{table-prefix-cls}-column-sorters {
    &:before,
    &:hover:before {
      content: none;
    }
  }

  &-thead > tr > th {
    .@{table-prefix-cls}-column-sorter {
      &-up,
      &-down {
        &.on {
          color: @table-header-icon-active-color;
        }
      }
    }
  }

  &-tbody > tr&-row {
    &:hover,
    &:focus,
    &:focus-within {
      & > td {
        background: @table-row-hover-bg;
      }
    }
  }

  // Custom styles

  &-headerless &-tbody > tr:first-child > td {
    border-top: @border-width-base @border-style-base @border-color-split;
  }
}

// List

.@{list-prefix-cls} {
  &-item {
    // custom rule
    &.selected {
      background-color: #f6f8f9;
    }

    &.disabled {
      background-color: fade(#f6f8f9, 40%);

      & > * {
        opacity: 0.4;
      }
    }
  }
}

.@{dialog-prefix-cls} {
  // styling for short modals (no lines)
  &.shortModal {
    .@{dialog-prefix-cls} {
      &-header,
      &-footer {
        border: none;
        padding: 16px;
      }

      &-body {
        padding: 10px 16px;
      }

      &-close-x {
        width: 46px;
        height: 46px;
        line-height: 46px;
      }
    }
  }

  // fullscreen modals
  &-fullscreen {
    .@{dialog-prefix-cls} {
      position: absolute;
      left: 15px;
      top: 15px;
      right: 15px;
      bottom: 15px;
      width: auto !important;
      height: auto !important;
      max-width: none;
      max-height: none;
      margin: 0;
      padding: 0;

      .@{dialog-prefix-cls}-content {
        position: absolute;
        left: 0;
        top: 0;
        right: 0;
        bottom: 0;
        width: auto;
        height: auto;
        margin: 0;
        padding: 0;
        display: flex;
        flex-direction: column;
      }

      .@{dialog-prefix-cls}-body {
        flex: 1 1 auto;
        overflow: auto;
      }
    }
  }
}

// description in modal header
.modal-header-desc {
  font-size: @font-size-base;
  color: @text-color-secondary;
  font-weight: normal;
  margin-top: 4px;
}

// Notification overrides
.@{notification-prefix-cls} {
  // vertical centering
  &-notice-close {
    top: 20px;
    right: 20px;
  }

  &-notice-description {
    max-width: 484px;
  }
}

.@{btn-prefix-cls} .@{iconfont-css-prefix}-ellipsis {
  margin: 0 -7px 0 -8px;
}

// Collapse

.@{collapse-prefix-cls} {
  &&-headerless {
    border: 0;
    background: none;

    .@{collapse-prefix-cls}-header {
      display: none;
    }

    .@{collapse-prefix-cls}-item,
    .@{collapse-prefix-cls}-content {
      border: 0;
    }

    .@{collapse-prefix-cls}-content-box {
      padding: 0;
    }
  }
}

// overrides for tall form components such as ace editor
.@{form-prefix-cls}-item {
  &-children {
    display: block; // so feeback icon positions correctly
  }

  // no change for short components, sticks to body for tall ones
  &-children-icon {
    top: auto !important;
    bottom: 8px;

    // makes the icon white instead of see-through
    & svg {
      background: white;
      border-radius: 50%;
    }
  }

  // for form items that contain text
  &.form-item-line-height-normal .@{form-prefix-cls}-item-control {
    line-height: 20px;
    margin-top: 9px;
  }
}

.@{menu-prefix-cls} {
  // invert stripe position with class .invert-stripe-position
  &-inline.invert-stripe-position {
    .@{menu-prefix-cls}-item {
      &::after {
        right: auto;
        left: 0;
      }
    }

    &:focus,
    &:focus-within {
      color: @menu-highlight-color;
    }
  }
}

.@{dropdown-prefix-cls}-menu-item {
  &:focus,
  &:focus-within {
    background-color: @item-hover-bg;
  }
}

// overrides for checkbox
@checkbox-prefix-cls: ~"@{ant-prefix}-checkbox";

.@{checkbox-prefix-cls}-wrapper + span,
.@{checkbox-prefix-cls} + span {
  padding-right: 0;
}

// make sure Multiple select has room for icons
.@{select-prefix-cls}-multiple {
  &.@{select-prefix-cls}-show-arrow,
  &.@{select-prefix-cls}-show-search,
  &.@{select-prefix-cls}-loading {
    .@{select-prefix-cls}-selector {
      padding-right: 30px;
    }
  }
}
