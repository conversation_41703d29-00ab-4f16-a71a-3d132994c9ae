.PHONY: compose_build up test_db create_database clean down bundle tests lint backend-unit-tests frontend-unit-tests test build watch start redis-cli bash

compose_build:
	docker-compose build

up:
	docker-compose up -d --build --remove-orphans

build:
	docker build -t $(TAG) .

test_db:
	@for i in `seq 1 5`; do \
		if (docker-compose exec postgres sh -c 'psql -U postgres -c "select 1;"' 2>&1 > /dev/null) then break; \
		else echo "postgres initializing..."; sleep 5; fi \
	done
	docker-compose exec postgres sh -c 'psql -U postgres -c "drop database if exists tests;" && psql -U postgres -c "create database tests;"'

create_database:
	docker-compose run server create_db

clean:
	docker-compose down && docker-compose rm

down:
	docker-compose down

bundle:
	docker-compose run server bin/bundle-extensions

tests:
	docker-compose run server tests

lint:
	./bin/flake8_tests.sh

backend-unit-tests: up test_db
	docker-compose run --rm --name tests server tests

frontend-unit-tests: bundle
	CYPRESS_INSTALL_BINARY=0 PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=1 npm ci
	npm run bundle
	npm test

test: lint backend-unit-tests frontend-unit-tests

npm_build: bundle
	npm run build

npm_watch: bundle
	npm run watch

npm_start: bundle
	npm run start

redis-cli:
	docker-compose run --rm redis redis-cli -h redis

bash:
	docker-compose run --rm server bash

tag := $(TAG)
branch_name := $(BRANCH)

push_dev:
	docker buildx build --platform=linux/amd64,linux/arm64  --cache-from type=local,src=/data/docker_cache/redash-internal/${branch_name} \
	--cache-to type=local,dest=/data/docker_cache/redash-internal/${branch_name},mode=max --push -t 125719378300.dkr.ecr.ap-southeast-1.amazonaws.com/zdp/redash:dev .

push_prod:
	docker buildx build --platform=linux/amd64,linux/arm64  --cache-from type=local,src=/data/docker_cache/redash-internal/${branch_name}\
        --cache-to type=local,dest=/data/docker_cache/redash-internal/${branch_name},mode=max --push -t 125719378300.dkr.ecr.ap-southeast-1.amazonaws.com/zdp/redash:${tag} .
