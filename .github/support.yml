# Configuration for Support Requests - https://github.com/dessant/support-requests

# Label used to mark issues as support requests
supportLabel: Support Question

# Comment to post on issues marked as support requests, `{issue-author}` is an
# optional placeholder. Set to `false` to disable
supportComment: >
  :wave: @{issue-author}, we use the issue tracker exclusively for bug reports
  and planned work. However, this issue appears to be a support request.
  Please use [our forum](https://discuss.redash.io) to get help.

# Close issues marked as support requests
close: true

# Lock issues marked as support requests
lock: false

# Assign `off-topic` as the reason for locking. Set to `false` to disable
setLockReason: true

# Repository to extend settings from
# _extends: repo
