name: CI

on:
  release:
    types: [published]
  push:
    branches:
      - staging

permissions:
  pull-requests: write
  checks: write
  contents: write
  id-token: write

jobs:
  build:
    runs-on: [ self-hosted, 8v, od, acc-infraprod, arch-x64 ]

    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Set AWS Account ID based on Event Type
      id: set-aws-account-id
      run: |
        echo "${{ github.event_name }}"
        if [[ "${{ github.event_name }}" == "release" && github.event.release ]]; then
          echo "RUN_COMMAND=make push_prod TAG=${{ github.event.release.tag_name}} BRANCH=${{ github.ref }}" >> $GITHUB_ENV
        else
          echo "RUN_COMMAND=make push_dev BRANCH=${{ github.ref }}" >> $GITHUB_ENV
        fi
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Configure AWS Credentials
      uses: aws-actions/configure-aws-credentials@v2
      with:
        role-to-assume: arn:aws:iam::************:role/github-runner-role
        aws-region: ap-southeast-1

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2

    - name: Log in to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v1

    - name: Build and tag Docker image
      run: |
        ${{env.RUN_COMMAND}}
