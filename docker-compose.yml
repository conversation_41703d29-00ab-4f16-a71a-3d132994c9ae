# This configuration file is for the **development** setup.
# For a production example please refer to getredash/setup repository on GitHub.
version: "3.9"
x-redash-service: &redash-service
  build:
    context: .
    # args:
      # skip_frontend_build: ""
  volumes:
    - .:/app
  env_file:
    - .env
x-redash-environment: &redash-environment
  REDASH_LOG_LEVEL: "INFO"
  REDASH_REDIS_URL: "redis://redis:6379/0"
  REDASH_DATABASE_URL: "****************************************/redash"
  REDASH_RATELIMIT_ENABLED: "false"
  REDASH_MAIL_DEFAULT_SENDER: "<EMAIL>"
  REDASH_MAIL_SERVER: "email"
  REDASH_FEATURE_ENFORCE_QUERY_ROWS_LIMIT: "true"
  REDASH_COOKIE_SECRET: akdgksdbkewqb
  QUEUES: "queries,scheduled_queries,celery,schemas,push_to_jumbo"
  REDASH_WORKER_TIMEOUT: 1200
  # Set secret keys in the .env file
services:
  server:
    <<: *redash-service
    command: server
    depends_on:
      - postgres
      - redis
      - statsd-exporter
    ports:
      - "5001:5000"
      - "5678:5678"
      - "8081:8080"
    environment:
      <<: *redash-environment
      PYTHONUNBUFFERED: 0

  scheduler:
    <<: *redash-service
    command: scheduler
    depends_on:
      - server
    environment:
      <<: *redash-environment

  worker:
    <<: *redash-service
    command: worker
    depends_on:
      - server
    environment:
      <<: *redash-environment
      PYTHONUNBUFFERED: 0
      WORKERS_COUNT: 1

  redis:
    image: redis:5
    restart: unless-stopped

  postgres:
    image: postgres:15.5
    # The following turns the DB into less durable, but gains significant performance improvements for the tests run (x3
    # improvement on my personal machine). We should consider moving this into a dedicated Docker Compose configuration for
    # tests.
    ports:
      - "5432:5432"
    command: "postgres -c fsync=off -c full_page_writes=off -c synchronous_commit=OFF"
    restart: unless-stopped
    environment:
      POSTGRES_HOST_AUTH_METHOD: "trust"
      POSTGRES_USER: redash
      POSTGRES_PASSWORD: redash
      POSTGRES_DB: redash

  statsd-exporter:
    hostname: statsd-exporter
    container_name: statsd-exporter
    image: prom/statsd-exporter:v0.15.0
    volumes:
      - ./redash-statsd-mapping.yml:/etc/prometheus/statsd-mapping.yaml
    command:
      - --statsd.mapping-config=/etc/prometheus/statsd-mapping.yaml
    ports:
      - 9102:9102
      - 9125:9125/udp

  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: redis-commander
    environment:
      REDIS_HOSTS: local:redis:6379
    restart: unless-stopped
    ports:
      - 8082:8081
