version: 2.0

build-docker-image-job: &build-docker-image-job
  docker:
    - image: circleci/node:12
  steps:
    - setup_remote_docker
    - checkout
    - run: sudo apt update
    - run: sudo apt install python3-pip
    - run: sudo pip3 install -r requirements_bundles.txt
    - run: .circleci/update_version
    - run: npm run bundle
    - run: .circleci/docker_build
jobs:
  backend-lint:
    docker:
      - image: circleci/python:3.7.0
    steps:
      - checkout
      - run: sudo pip install flake8
      - run: ./bin/flake8_tests.sh
  backend-unit-tests:
    environment:
      COMPOSE_FILE: .circleci/docker-compose.circle.yml
      COMPOSE_PROJECT_NAME: redash
    docker:
      - image: circleci/buildpack-deps:xenial
    steps:
      - setup_remote_docker
      - checkout
      - run:
          name: Build Docker Images
          command: |
            set -x
            docker-compose build --build-arg skip_ds_deps=true --build-arg skip_frontend_build=true
            docker-compose up -d
            sleep 10
      - run:
          name: Create Test Database
          command: docker-compose run --rm postgres psql -h postgres -U postgres -c "create database tests;"
      - run:
          name: List Enabled Query Runners
          command: docker-compose run --rm redash manage ds list_types
      - run:
          name: Run Tests
          command: docker-compose run --name tests redash tests --junitxml=junit.xml --cov-report xml --cov=redash --cov-config .coveragerc tests/
      - run:
          name: Copy Test Results
          command: |
            mkdir -p /tmp/test-results/unit-tests
            docker cp tests:/app/coverage.xml ./coverage.xml
            docker cp tests:/app/junit.xml /tmp/test-results/unit-tests/results.xml
          when: always
      - store_test_results:
          path: /tmp/test-results
      - store_artifacts:
          path: coverage.xml
  frontend-lint:
    environment:
      CYPRESS_INSTALL_BINARY: 0
      PUPPETEER_SKIP_CHROMIUM_DOWNLOAD: 1
    docker:
      - image: circleci/node:12
    steps:
      - checkout
      - run: mkdir -p /tmp/test-results/eslint
      - run: npm ci
      - run: npm run lint:ci
      - store_test_results:
          path: /tmp/test-results
  frontend-unit-tests:
    environment:
      CYPRESS_INSTALL_BINARY: 0
      PUPPETEER_SKIP_CHROMIUM_DOWNLOAD: 1
    docker:
      - image: circleci/node:12
    steps:
      - checkout
      - run: sudo apt update
      - run: sudo apt install python3-pip
      - run: sudo pip3 install -r requirements_bundles.txt
      - run: npm ci
      - run: npm run bundle
      - run:
          name: Run App Tests
          command: npm test
      - run:
          name: Run Visualizations Tests
          command: (cd viz-lib && npm test)
      - run: npm run lint
  frontend-e2e-tests:
    environment:
      COMPOSE_FILE: .circleci/docker-compose.cypress.yml
      COMPOSE_PROJECT_NAME: cypress
      PERCY_TOKEN_ENCODED: ZGRiY2ZmZDQ0OTdjMzM5ZWE0ZGQzNTZiOWNkMDRjOTk4Zjg0ZjMxMWRmMDZiM2RjOTYxNDZhOGExMjI4ZDE3MA==
      CYPRESS_PROJECT_ID_ENCODED: OTI0Y2th
      CYPRESS_RECORD_KEY_ENCODED: YzA1OTIxMTUtYTA1Yy00NzQ2LWEyMDMtZmZjMDgwZGI2ODgx
      CYPRESS_INSTALL_BINARY: 0
      PUPPETEER_SKIP_CHROMIUM_DOWNLOAD: 1
    docker:
      - image: circleci/node:12
    steps:
      - setup_remote_docker
      - checkout
      - run:
          name: Enable Code Coverage report for master branch
          command: |
            if [ "$CIRCLE_BRANCH" = "master" ]; then
              echo 'export CODE_COVERAGE=true' >> $BASH_ENV
              source $BASH_ENV
            fi
      - run:
          name: Install npm dependencies
          command: |
            npm ci
      - run:
          name: Setup Redash server
          command: |
            npm run cypress build
            npm run cypress start -- --skip-db-seed
            docker-compose run cypress npm run cypress db-seed
      - run:
          name: Execute Cypress tests
          command: npm run cypress run-ci
      - run:
          name: "Failure: output container logs to console"
          command: |
            docker-compose logs
          when: on_fail
      - run:
          name: Copy Code Coverage results
          command: |
            docker cp cypress:/usr/src/app/coverage ./coverage || true
          when: always
      - store_artifacts:
          path: coverage
  build-docker-image: *build-docker-image-job
  build-preview-docker-image: *build-docker-image-job
workflows:
  version: 2
  build:
    jobs:
      - backend-lint
      - backend-unit-tests:
          requires:
            - backend-lint
      - frontend-lint
      - frontend-unit-tests:
          requires:
            - backend-lint
            - frontend-lint
      - frontend-e2e-tests:
          requires:
            - frontend-lint
      - build-preview-docker-image:
          requires:
            - backend-unit-tests
            - frontend-unit-tests
            - frontend-e2e-tests
          filters:
            branches:
              only:
                - master
      - hold:
          type: approval
          requires:
            - backend-unit-tests
            - frontend-unit-tests
            - frontend-e2e-tests
          filters:
            branches:
              only:
                - /release\/.*/
      - build-docker-image:
          requires:
            - hold
