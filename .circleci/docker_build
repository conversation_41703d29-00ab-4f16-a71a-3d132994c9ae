#!/bin/bash
VERSION=$(jq -r .version package.json)
VERSION_TAG=$VERSION.b$CIRCLE_BUILD_NUM

docker login -u $DOCKER_USER -p $DOCKER_PASS

if [ $CIRCLE_BRANCH = master ] || [ $CIRCLE_BRANCH = preview-image ]
then
    docker build --build-arg skip_dev_deps=true -t redash/redash:preview -t redash/preview:$VERSION_TAG .
    docker push redash/redash:preview
    docker push redash/preview:$VERSION_TAG
else
    docker build --build-arg skip_dev_deps=true -t redash/redash:$VERSION_TAG .
    docker push redash/redash:$VERSION_TAG
fi

echo "Built: $VERSION_TAG"