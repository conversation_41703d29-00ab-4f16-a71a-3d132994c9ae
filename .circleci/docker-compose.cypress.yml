version: "2.2"
x-redash-service: &redash-service
  build:
    context: ../
    args:
      skip_dev_deps: ""
      skip_ds_deps: ""
      code_coverage: ${CODE_COVERAGE}
x-redash-environment: &redash-environment
  REDASH_LOG_LEVEL: "INFO"
  REDASH_REDIS_URL: "redis://redis:6379/0"
  REDASH_DATABASE_URL: "postgresql://postgres@postgres/postgres"
  REDASH_RATELIMIT_ENABLED: "false"
  REDASH_ENFORCE_CSRF: "true"
  REDASH_COOKIE_SECRET: "2H9gNG9obnAQ9qnR9BDTQUph6CbXKCzF"
services:
  server:
    <<: *redash-service
    command: server
    depends_on:
      - postgres
      - redis
    ports:
      - "5000:5000"
    environment:
      <<: *redash-environment
      PYTHONUNBUFFERED: 0
  scheduler:
    <<: *redash-service
    command: scheduler
    depends_on:
      - server
    environment:
      <<: *redash-environment
  worker:
    <<: *redash-service
    command: worker
    depends_on:
      - server
    environment:
      <<: *redash-environment
      PYTHONUNBUFFERED: 0
  cypress:
    ipc: host
    build:
      context: ../
      dockerfile: .circleci/Dockerfile.cypress
    depends_on:
      - server
      - worker
      - scheduler
    environment:
      CYPRESS_baseUrl: "http://server:5000"
      CYPRESS_coverage: ${CODE_COVERAGE}
      PERCY_TOKEN: ${PERCY_TOKEN}
      PERCY_BRANCH: ${CIRCLE_BRANCH}
      PERCY_COMMIT: ${CIRCLE_SHA1}
      PERCY_PULL_REQUEST: ${CIRCLE_PR_NUMBER}
      COMMIT_INFO_BRANCH: ${CIRCLE_BRANCH}
      COMMIT_INFO_MESSAGE: ${COMMIT_INFO_MESSAGE}
      COMMIT_INFO_AUTHOR: ${CIRCLE_USERNAME}
      COMMIT_INFO_SHA: ${CIRCLE_SHA1}
      COMMIT_INFO_REMOTE: ${CIRCLE_REPOSITORY_URL}
      CYPRESS_PROJECT_ID: ${CYPRESS_PROJECT_ID}
      CYPRESS_RECORD_KEY: ${CYPRESS_RECORD_KEY}
  redis:
    image: redis:3.0-alpine
    restart: unless-stopped
  postgres:
    image: postgres:9.5.6-alpine
    command: "postgres -c fsync=off -c full_page_writes=off -c synchronous_commit=OFF"
    restart: unless-stopped
